@echo off
cd /d "%~dp0"

:: Check for admin privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo This script requires Administrator privileges.
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

if not exist "%cd%\nssm.exe" (
    echo NSSM not found! Download from https://nssm.cc/download
    pause
    exit /b 1
)

%cd%\nssm.exe stop vfsm
%cd%\nssm.exe remove vfsm confirm

timeout /t 5 >nul