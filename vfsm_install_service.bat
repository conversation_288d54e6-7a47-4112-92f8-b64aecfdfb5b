@echo off
cd /d "%~dp0"

:: Check for admin privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo This script requires Administrator privileges.
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

if not exist "%cd%\nssm.exe" (
    echo NSSM not found! Download from https://nssm.cc/download
    pause
    exit /b 1
)

"%cd%\nssm.exe" install vfsm "%cd%\vfs_mon.exe"
"%cd%\nssm.exe" set vfsm Start SERVICE_AUTO_START
"%cd%\nssm.exe" set vfsm Type SERVICE_WIN32_OWN_PROCESS
"%cd%\nssm.exe" set vfsm AppNoConsole 1
"%cd%\nssm.exe" start vfsm

echo Service installed successfully!
timeout /t 5 >nul



